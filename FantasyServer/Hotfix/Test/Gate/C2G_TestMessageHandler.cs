
using Fantasy;
using Fantasy.Async;
using Fantasy.Network;
using Fantasy.Network.Interface;
using Fantasy.Platform.Net;

namespace Demo
{
    public class C2G_TestMessageHandler : Message<C2G_TestMessage>
    {
        protected override async FTask Run(Session session, C2G_TestMessage message)
        {
            Console.WriteLine($"Gate received client message: {message.Tag}");
            var chatSceneConfigs = SceneConfigData.Instance.GetSceneBySceneType(SceneType.Chat);
            var chatSceneConfig = chatSceneConfigs[0];
            session.Scene.NetworkMessagingComponent.SendInnerRoute(chatSceneConfig.RouteId, new G2Chat_TestMessage(){Tag = "Message, Server Chat!"});
            var resp = await session.Scene.NetworkMessagingComponent.CallInnerRoute(chatSceneConfig.RouteId, new G2Chat_TestRequest(){Tag = "Request, Server Chat!"}) as Chat2G_TestResponse;
            Console.WriteLine(resp != null ? $"Gate received chat response: {resp.Tag}" : "Response is null");
            await FTask.CompletedTask;
        }
    }
}
