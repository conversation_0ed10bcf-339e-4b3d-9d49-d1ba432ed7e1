using Fantasy;
using Fantasy.Async;
using Fantasy.Network.Interface;

namespace Demo
{
    public class G2Chat_TestRequestHandler : RouteRPC<Scene, G2Chat_TestRequest, Chat2G_TestResponse>
    {
        protected override async FTask Run(Scene entity, G2Chat_TestRequest request, Chat2G_TestResponse response, Action reply)
        {
            Console.WriteLine($"Chat received gate request: {request.Tag}");
            response.Tag = $"Chat response to: {request.Tag}";
            await FTask.CompletedTask;
        }
    }
}
